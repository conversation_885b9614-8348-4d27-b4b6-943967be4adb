#include <iostream>
#include <iomanip>
#include "cubic_polynomial_2d.h"

using namespace CubicPolynomial2D;

int main() {
    std::cout << "=== 2D Cubic Polynomial Demo ===" << std::endl;
    std::cout << std::fixed << std::setprecision(3);

    // Demo 1: Point-Heading-Point-Heading fitting
    std::cout << "\n1. Point-Heading-Point-Heading Fitting:" << std::endl;
    
    Point2D start_point(0.0, 0.0);
    Heading2D start_heading(1.0, 0.0);  // Moving right
    Point2D end_point(3.0, 2.0);
    Heading2D end_heading(0.0, 1.0);    // Moving up
    
    std::cout << "Start: (" << start_point.x() << ", " << start_point.y() 
              << ") with heading (" << start_heading.x() << ", " << start_heading.y() << ")" << std::endl;
    std::cout << "End: (" << end_point.x() << ", " << end_point.y() 
              << ") with heading (" << end_heading.x() << ", " << end_heading.y() << ")" << std::endl;

    Coefficients2D coeffs = fitPointHeadingPointHeading2D(start_point, start_heading, end_point, end_heading);
    
    std::cout << "\nFitted polynomial:" << std::endl;
    std::cout << toString(coeffs) << std::endl;

    // Evaluate at several points
    std::cout << "\nEvaluation along the curve:" << std::endl;
    std::cout << "s\tx(s)\ty(s)\tdx/ds\tdy/ds" << std::endl;
    for (double s = 0.0; s <= 1.0; s += 0.25) {
        Point2D point = evaluate(coeffs, s);
        Heading2D tangent = evaluateDerivative(coeffs, s);
        std::cout << s << "\t" << point.x() << "\t" << point.y() 
                  << "\t" << tangent.x() << "\t" << tangent.y() << std::endl;
    }

    // Demo 2: Four Points fitting
    std::cout << "\n\n2. Four Points Fitting:" << std::endl;
    
    std::vector<Point2D> points = {
        Point2D(0.0, 0.0),
        Point2D(1.0, 1.0),
        Point2D(2.0, 1.5),
        Point2D(3.0, 0.0)
    };
    std::vector<double> s_values = {0.0, 0.33, 0.67, 1.0};
    
    std::cout << "Fitting through points:" << std::endl;
    for (size_t i = 0; i < points.size(); ++i) {
        std::cout << "s=" << s_values[i] << ": (" << points[i].x() << ", " << points[i].y() << ")" << std::endl;
    }

    Coefficients2D coeffs2 = fitFourPoints2D(points, s_values);
    
    std::cout << "\nFitted polynomial:" << std::endl;
    std::cout << toString(coeffs2) << std::endl;

    // Verify the fit
    std::cout << "\nVerification (should match input points):" << std::endl;
    std::cout << "s\tExpected\t\tActual" << std::endl;
    for (size_t i = 0; i < points.size(); ++i) {
        Point2D actual = evaluate(coeffs2, s_values[i]);
        std::cout << s_values[i] << "\t(" << points[i].x() << ", " << points[i].y() 
                  << ")\t\t(" << actual.x() << ", " << actual.y() << ")" << std::endl;
    }

    // Demo 3: Curvature analysis
    std::cout << "\n\n3. Curvature Analysis:" << std::endl;
    std::cout << "s\tCurvature Vector (d²x/ds², d²y/ds²)" << std::endl;
    for (double s = 0.0; s <= 1.0; s += 0.25) {
        Heading2D curvature = evaluateSecondDerivative(coeffs, s);
        std::cout << s << "\t(" << curvature.x() << ", " << curvature.y() << ")" << std::endl;
    }

    return 0;
}
