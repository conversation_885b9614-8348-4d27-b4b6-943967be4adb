#include "cubic_polynomial.h"
#include <sstream>
#include <iomanip>
#include <cmath>

namespace CubicPolynomial {
    double evaluate(const Coefficients &coeffs, double s) {
        const double a = coeffs[0];
        const double b = coeffs[1];
        const double c = coeffs[2];
        const double d = coeffs[3];

        return a * s * s * s + b * s * s + c * s + d;
    }

    double evaluateDerivative(const Coefficients &coeffs, double s) {
        const double a = coeffs[0];
        const double b = coeffs[1];
        const double c = coeffs[2];

        return 3.0 * a * s * s + 2.0 * b * s + c;
    }

    double evaluateSecondDerivative(const Coefficients &coeffs, double s) {
        const double a = coeffs[0];
        const double b = coeffs[1];

        return 6.0 * a * s + 2.0 * b;
    }

    Coefficients fitFourPoints1D(const std::vector<double> &points1D, std::vector<double> &s_values) {
        if (s_values.empty()) {
            s_values = std::vector<double>({0.0, 0.3333, 0.6666, 1.0});
        }
        if (points1D.size() != 4 || s_values.size() != 4) {
            throw std::invalid_argument("Exactly 4 points and sValues required for cubic polynomial fitting");
        }

        // Set up the Vandermonde matrix
        // [s1³ s1² s1 1] [a]   [x1]
        // [s2³ s2² s2 1] [b] = [x2]
        // [s3³ s3² s3 1] [c]   [x3]
        // [s4³ s4² s4 1] [d]   [x4]

        Eigen::Matrix4d A;
        Eigen::Vector4d b;

        for (int i = 0; i < 4; ++i) {
            const double s = s_values[i];
            const double x = points1D[i];

            A(i, 0) = s * s * s; // s³
            A(i, 1) = s * s; // s²
            A(i, 2) = s; // s
            A(i, 3) = 1.0; // 1

            b[i] = x;
        }

        // Solve the linear system
        Coefficients coeffs = A.colPivHouseholderQr().solve(b);

        return coeffs;
    }

    Coefficients fitPointSlopePointSlope1D(
        const double &point0, const double &slope0,
        const double &point1, const double &slope1,
        const double &s0, const double &s1
    ) {
        const double x0 = point0;
        const double m0 = slope0;
        const double x1 = point1;
        const double m1 = slope1;

        // Set up the system of equations for Hermite interpolation:
        // f(s0) = x0    =>  a*s0³ + b*s0² + c*s0 + d = x0
        // f(s1) = x1    =>  a*s1³ + b*s1² + c*s1 + d = x1
        // f'(s0) = m0   =>  3*a*s0² + 2*b*s0 + c = m0
        // f'(s1) = m1   =>  3*a*s1² + 2*b*s1 + c = m1

        Eigen::Matrix4d A;
        Eigen::Vector4d rhs;

        // f(s0) = x0
        A(0, 0) = s0 * s0 * s0;
        A(0, 1) = s0 * s0;
        A(0, 2) = s0;
        A(0, 3) = 1.0;
        rhs[0] = x0;

        // f(s1) = x1
        A(1, 0) = s1 * s1 * s1;
        A(1, 1) = s1 * s1;
        A(1, 2) = s1;
        A(1, 3) = 1.0;
        rhs[1] = x1;

        // f'(s0) = m0
        A(2, 0) = 3.0 * s0 * s0;
        A(2, 1) = 2.0 * s0;
        A(2, 2) = 1.0;
        A(2, 3) = 0.0;
        rhs[2] = m0;

        // f'(s1) = m1
        A(3, 0) = 3.0 * s1 * s1;
        A(3, 1) = 2.0 * s1;
        A(3, 2) = 1.0;
        A(3, 3) = 0.0;
        rhs[3] = m1;

        // Solve the linear system
        Coefficients coeffs = A.colPivHouseholderQr().solve(rhs);

        return coeffs;
    }

    std::string toString(const Coefficients &coeffs) {
        std::ostringstream oss;
        oss << std::fixed << std::setprecision(3);

        const double a = coeffs[0];
        const double b = coeffs[1];
        const double c = coeffs[2];
        const double d = coeffs[3];

        bool first = true;

        // Handle s³ term
        if (std::abs(a) > 1e-10) {
            if (!first && a > 0) oss << " + ";
            if (a < 0) oss << " - ";
            if (std::abs(a) != 1.0 || first) {
                oss << std::abs(a);
            }
            oss << "s³";
            first = false;
        }

        // Handle s² term
        if (std::abs(b) > 1e-10) {
            if (!first && b > 0) oss << " + ";
            if (b < 0) oss << " - ";
            if (std::abs(b) != 1.0 || first) {
                oss << std::abs(b);
            }
            oss << "s²";
            first = false;
        }

        // Handle s term
        if (std::abs(c) > 1e-10) {
            if (!first && c > 0) oss << " + ";
            if (c < 0) oss << " - ";
            if (std::abs(c) != 1.0 || first) {
                oss << std::abs(c);
            }
            oss << "s";
            first = false;
        }

        // Handle constant term
        if (std::abs(d) > 1e-10 || first) {
            if (!first && d > 0) oss << " + ";
            if (d < 0 && !first) oss << " - ";
            oss << (first ? d : std::abs(d));
        }

        return first ? "0" : oss.str();
    }

    std::vector<double> evaluateMultiple(const Coefficients &coeffs, const std::vector<double> &s_values) {
        std::vector<double> results;
        results.reserve(s_values.size());

        for (double s: s_values) {
            results.push_back(evaluate(coeffs, s));
        }

        return results;
    }
} // namespace CubicPolynomial
