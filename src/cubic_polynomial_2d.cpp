#include "cubic_polynomial_2d.h"
#include <sstream>
#include <iomanip>
#include <cmath>

namespace CubicPolynomial2D {
    Point2D evaluate(const Coefficients2D &coeffs, double s) {
        // Extract x coefficients: [a₁, b₁, c₁, d₁]
        const double a1 = coeffs[0];
        const double b1 = coeffs[1];
        const double c1 = coeffs[2];
        const double d1 = coeffs[3];

        // Extract y coefficients: [a₂, b₂, c₂, d₂]
        const double a2 = coeffs[4];
        const double b2 = coeffs[5];
        const double c2 = coeffs[6];
        const double d2 = coeffs[7];

        // Evaluate x(s) = a₁s³ + b₁s² + c₁s + d₁
        const double x = a1 * s * s * s + b1 * s * s + c1 * s + d1;
        
        // Evaluate y(s) = a₂s³ + b₂s² + c₂s + d₂
        const double y = a2 * s * s * s + b2 * s * s + c2 * s + d2;

        return Point2D(x, y);
    }

    Heading2D evaluateDerivative(const Coefficients2D &coeffs, double s) {
        // Extract x coefficients: [a₁, b₁, c₁, d₁]
        const double a1 = coeffs[0];
        const double b1 = coeffs[1];
        const double c1 = coeffs[2];

        // Extract y coefficients: [a₂, b₂, c₂, d₂]
        const double a2 = coeffs[4];
        const double b2 = coeffs[5];
        const double c2 = coeffs[6];

        // Evaluate x'(s) = 3a₁s² + 2b₁s + c₁
        const double dx_ds = 3.0 * a1 * s * s + 2.0 * b1 * s + c1;
        
        // Evaluate y'(s) = 3a₂s² + 2b₂s + c₂
        const double dy_ds = 3.0 * a2 * s * s + 2.0 * b2 * s + c2;

        return Heading2D(dx_ds, dy_ds);
    }

    Heading2D evaluateSecondDerivative(const Coefficients2D &coeffs, double s) {
        // Extract x coefficients: [a₁, b₁, c₁, d₁]
        const double a1 = coeffs[0];
        const double b1 = coeffs[1];

        // Extract y coefficients: [a₂, b₂, c₂, d₂]
        const double a2 = coeffs[4];
        const double b2 = coeffs[5];

        // Evaluate x''(s) = 6a₁s + 2b₁
        const double d2x_ds2 = 6.0 * a1 * s + 2.0 * b1;
        
        // Evaluate y''(s) = 6a₂s + 2b₂
        const double d2y_ds2 = 6.0 * a2 * s + 2.0 * b2;

        return Heading2D(d2x_ds2, d2y_ds2);
    }

    Coefficients2D fitFourPoints2D(const std::vector<Point2D> &points, std::vector<double> &s_values) {
        if (s_values.empty()) {
            s_values = std::vector<double>({0.0, 0.3333, 0.6666, 1.0});
        }
        if (points.size() != 4 || s_values.size() != 4) {
            throw std::invalid_argument("Exactly 4 points and sValues required for cubic polynomial fitting");
        }

        // Set up the Vandermonde matrix for both x and y components
        // [s₁³ s₁² s₁ 1] [a₁]   [x₁]     [s₁³ s₁² s₁ 1] [a₂]   [y₁]
        // [s₂³ s₂² s₂ 1] [b₁] = [x₂]     [s₂³ s₂² s₂ 1] [b₂] = [y₂]
        // [s₃³ s₃² s₃ 1] [c₁]   [x₃]     [s₃³ s₃² s₃ 1] [c₂]   [y₃]
        // [s₄³ s₄² s₄ 1] [d₁]   [x₄]     [s₄³ s₄² s₄ 1] [d₂]   [y₄]

        Eigen::Matrix4d A;
        Eigen::Vector4d b_x, b_y;

        for (int i = 0; i < 4; ++i) {
            const double s = s_values[i];
            const Point2D &point = points[i];

            A(i, 0) = s * s * s; // s³
            A(i, 1) = s * s; // s²
            A(i, 2) = s; // s
            A(i, 3) = 1.0; // 1

            b_x[i] = point.x();
            b_y[i] = point.y();
        }

        // Solve the linear systems for both x and y components
        Eigen::Vector4d x_coeffs = A.colPivHouseholderQr().solve(b_x);
        Eigen::Vector4d y_coeffs = A.colPivHouseholderQr().solve(b_y);

        // Combine into 2D coefficients
        return combineCoeffs(x_coeffs, y_coeffs);
    }

    Coefficients2D fitPointHeadingPointHeading2D(
        const Point2D &point0, const Heading2D &heading0,
        const Point2D &point1, const Heading2D &heading1,
        const double &s0, const double &s1
    ) {
        // Set up the system of equations for Hermite interpolation:
        // P(s₀) = point₀    =>  x(s₀) = x₀, y(s₀) = y₀
        // P(s₁) = point₁    =>  x(s₁) = x₁, y(s₁) = y₁
        // P'(s₀) = heading₀ =>  x'(s₀) = hx₀, y'(s₀) = hy₀
        // P'(s₁) = heading₁ =>  x'(s₁) = hx₁, y'(s₁) = hy₁

        Eigen::Matrix4d A;
        Eigen::Vector4d rhs_x, rhs_y;

        // P(s₀) = point₀
        A(0, 0) = s0 * s0 * s0;
        A(0, 1) = s0 * s0;
        A(0, 2) = s0;
        A(0, 3) = 1.0;
        rhs_x[0] = point0.x();
        rhs_y[0] = point0.y();

        // P(s₁) = point₁
        A(1, 0) = s1 * s1 * s1;
        A(1, 1) = s1 * s1;
        A(1, 2) = s1;
        A(1, 3) = 1.0;
        rhs_x[1] = point1.x();
        rhs_y[1] = point1.y();

        // P'(s₀) = heading₀
        A(2, 0) = 3.0 * s0 * s0;
        A(2, 1) = 2.0 * s0;
        A(2, 2) = 1.0;
        A(2, 3) = 0.0;
        rhs_x[2] = heading0.x();
        rhs_y[2] = heading0.y();

        // P'(s₁) = heading₁
        A(3, 0) = 3.0 * s1 * s1;
        A(3, 1) = 2.0 * s1;
        A(3, 2) = 1.0;
        A(3, 3) = 0.0;
        rhs_x[3] = heading1.x();
        rhs_y[3] = heading1.y();

        // Solve the linear systems for both x and y components
        Eigen::Vector4d x_coeffs = A.colPivHouseholderQr().solve(rhs_x);
        Eigen::Vector4d y_coeffs = A.colPivHouseholderQr().solve(rhs_y);

        // Combine into 2D coefficients
        return combineCoeffs(x_coeffs, y_coeffs);
    }

    std::string toString(const Coefficients2D &coeffs) {
        std::ostringstream oss;
        oss << std::fixed << std::setprecision(3);

        // Extract coefficients
        Eigen::Vector4d x_coeffs = getXCoeffs(coeffs);
        Eigen::Vector4d y_coeffs = getYCoeffs(coeffs);

        oss << "x(s) = ";
        
        // Format x polynomial
        bool first = true;
        for (int i = 0; i < 4; ++i) {
            double coeff = x_coeffs[i];
            if (std::abs(coeff) > 1e-10) {
                if (!first && coeff > 0) oss << " + ";
                if (coeff < 0) oss << " - ";
                
                double abs_coeff = std::abs(coeff);
                if (abs_coeff != 1.0 || i == 3) { // Always show coefficient for constant term
                    oss << abs_coeff;
                }
                
                if (i == 0) oss << "s³";
                else if (i == 1) oss << "s²";
                else if (i == 2) oss << "s";
                
                first = false;
            }
        }
        if (first) oss << "0";

        oss << "\ny(s) = ";
        
        // Format y polynomial
        first = true;
        for (int i = 0; i < 4; ++i) {
            double coeff = y_coeffs[i];
            if (std::abs(coeff) > 1e-10) {
                if (!first && coeff > 0) oss << " + ";
                if (coeff < 0) oss << " - ";
                
                double abs_coeff = std::abs(coeff);
                if (abs_coeff != 1.0 || i == 3) { // Always show coefficient for constant term
                    oss << abs_coeff;
                }
                
                if (i == 0) oss << "s³";
                else if (i == 1) oss << "s²";
                else if (i == 2) oss << "s";
                
                first = false;
            }
        }
        if (first) oss << "0";

        return oss.str();
    }

    std::vector<Point2D> evaluateMultiple(const Coefficients2D &coeffs, const std::vector<double> &s_values) {
        std::vector<Point2D> results;
        results.reserve(s_values.size());

        for (double s : s_values) {
            results.push_back(evaluate(coeffs, s));
        }

        return results;
    }

    Eigen::Vector4d getXCoeffs(const Coefficients2D &coeffs2d) {
        return coeffs2d.segment<4>(0);
    }

    Eigen::Vector4d getYCoeffs(const Coefficients2D &coeffs2d) {
        return coeffs2d.segment<4>(4);
    }

    Coefficients2D combineCoeffs(const Eigen::Vector4d &x_coeffs, const Eigen::Vector4d &y_coeffs) {
        Coefficients2D combined;
        combined.segment<4>(0) = x_coeffs;
        combined.segment<4>(4) = y_coeffs;
        return combined;
    }

} // namespace CubicPolynomial2D
