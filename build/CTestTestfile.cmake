# CMake generated Testfile for 
# Source directory: /home/<USER>/CubicPolynomialFitting
# Build directory: /home/<USER>/CubicPolynomialFitting/build
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
add_test(CubicPolynomialTests "/home/<USER>/CubicPolynomialFitting/build/test_cubic_polynomial")
set_tests_properties(CubicPolynomialTests PROPERTIES  _BACKTRACE_TRIPLES "/home/<USER>/CubicPolynomialFitting/CMakeLists.txt;49;add_test;/home/<USER>/CubicPolynomialFitting/CMakeLists.txt;0;")
add_test(CubicPolynomial2DTests "/home/<USER>/CubicPolynomialFitting/build/test_cubic_polynomial_2d")
set_tests_properties(CubicPolynomial2DTests PROPERTIES  _BACKTRACE_TRIPLES "/home/<USER>/CubicPolynomialFitting/CMakeLists.txt;50;add_test;/home/<USER>/CubicPolynomialFitting/CMakeLists.txt;0;")
