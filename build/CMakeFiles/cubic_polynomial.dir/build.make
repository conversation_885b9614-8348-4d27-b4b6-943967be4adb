# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/CubicPolynomialFitting

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/CubicPolynomialFitting/build

# Include any dependencies generated for this target.
include CMakeFiles/cubic_polynomial.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/cubic_polynomial.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/cubic_polynomial.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/cubic_polynomial.dir/flags.make

CMakeFiles/cubic_polynomial.dir/src/cubic_polynomial.cpp.o: CMakeFiles/cubic_polynomial.dir/flags.make
CMakeFiles/cubic_polynomial.dir/src/cubic_polynomial.cpp.o: ../src/cubic_polynomial.cpp
CMakeFiles/cubic_polynomial.dir/src/cubic_polynomial.cpp.o: CMakeFiles/cubic_polynomial.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/CubicPolynomialFitting/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/cubic_polynomial.dir/src/cubic_polynomial.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/cubic_polynomial.dir/src/cubic_polynomial.cpp.o -MF CMakeFiles/cubic_polynomial.dir/src/cubic_polynomial.cpp.o.d -o CMakeFiles/cubic_polynomial.dir/src/cubic_polynomial.cpp.o -c /home/<USER>/CubicPolynomialFitting/src/cubic_polynomial.cpp

CMakeFiles/cubic_polynomial.dir/src/cubic_polynomial.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/cubic_polynomial.dir/src/cubic_polynomial.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/CubicPolynomialFitting/src/cubic_polynomial.cpp > CMakeFiles/cubic_polynomial.dir/src/cubic_polynomial.cpp.i

CMakeFiles/cubic_polynomial.dir/src/cubic_polynomial.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/cubic_polynomial.dir/src/cubic_polynomial.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/CubicPolynomialFitting/src/cubic_polynomial.cpp -o CMakeFiles/cubic_polynomial.dir/src/cubic_polynomial.cpp.s

CMakeFiles/cubic_polynomial.dir/src/cubic_polynomial_2d.cpp.o: CMakeFiles/cubic_polynomial.dir/flags.make
CMakeFiles/cubic_polynomial.dir/src/cubic_polynomial_2d.cpp.o: ../src/cubic_polynomial_2d.cpp
CMakeFiles/cubic_polynomial.dir/src/cubic_polynomial_2d.cpp.o: CMakeFiles/cubic_polynomial.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/CubicPolynomialFitting/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/cubic_polynomial.dir/src/cubic_polynomial_2d.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/cubic_polynomial.dir/src/cubic_polynomial_2d.cpp.o -MF CMakeFiles/cubic_polynomial.dir/src/cubic_polynomial_2d.cpp.o.d -o CMakeFiles/cubic_polynomial.dir/src/cubic_polynomial_2d.cpp.o -c /home/<USER>/CubicPolynomialFitting/src/cubic_polynomial_2d.cpp

CMakeFiles/cubic_polynomial.dir/src/cubic_polynomial_2d.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/cubic_polynomial.dir/src/cubic_polynomial_2d.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/CubicPolynomialFitting/src/cubic_polynomial_2d.cpp > CMakeFiles/cubic_polynomial.dir/src/cubic_polynomial_2d.cpp.i

CMakeFiles/cubic_polynomial.dir/src/cubic_polynomial_2d.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/cubic_polynomial.dir/src/cubic_polynomial_2d.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/CubicPolynomialFitting/src/cubic_polynomial_2d.cpp -o CMakeFiles/cubic_polynomial.dir/src/cubic_polynomial_2d.cpp.s

# Object files for target cubic_polynomial
cubic_polynomial_OBJECTS = \
"CMakeFiles/cubic_polynomial.dir/src/cubic_polynomial.cpp.o" \
"CMakeFiles/cubic_polynomial.dir/src/cubic_polynomial_2d.cpp.o"

# External object files for target cubic_polynomial
cubic_polynomial_EXTERNAL_OBJECTS =

libcubic_polynomial.a: CMakeFiles/cubic_polynomial.dir/src/cubic_polynomial.cpp.o
libcubic_polynomial.a: CMakeFiles/cubic_polynomial.dir/src/cubic_polynomial_2d.cpp.o
libcubic_polynomial.a: CMakeFiles/cubic_polynomial.dir/build.make
libcubic_polynomial.a: CMakeFiles/cubic_polynomial.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/CubicPolynomialFitting/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX static library libcubic_polynomial.a"
	$(CMAKE_COMMAND) -P CMakeFiles/cubic_polynomial.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/cubic_polynomial.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/cubic_polynomial.dir/build: libcubic_polynomial.a
.PHONY : CMakeFiles/cubic_polynomial.dir/build

CMakeFiles/cubic_polynomial.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/cubic_polynomial.dir/cmake_clean.cmake
.PHONY : CMakeFiles/cubic_polynomial.dir/clean

CMakeFiles/cubic_polynomial.dir/depend:
	cd /home/<USER>/CubicPolynomialFitting/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/CubicPolynomialFitting /home/<USER>/CubicPolynomialFitting /home/<USER>/CubicPolynomialFitting/build /home/<USER>/CubicPolynomialFitting/build /home/<USER>/CubicPolynomialFitting/build/CMakeFiles/cubic_polynomial.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/cubic_polynomial.dir/depend

