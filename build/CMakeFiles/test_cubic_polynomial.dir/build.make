# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/CubicPolynomialFitting

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/CubicPolynomialFitting/build

# Include any dependencies generated for this target.
include CMakeFiles/test_cubic_polynomial.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/test_cubic_polynomial.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/test_cubic_polynomial.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/test_cubic_polynomial.dir/flags.make

CMakeFiles/test_cubic_polynomial.dir/tests/test_cubic_polynomial.cpp.o: CMakeFiles/test_cubic_polynomial.dir/flags.make
CMakeFiles/test_cubic_polynomial.dir/tests/test_cubic_polynomial.cpp.o: ../tests/test_cubic_polynomial.cpp
CMakeFiles/test_cubic_polynomial.dir/tests/test_cubic_polynomial.cpp.o: CMakeFiles/test_cubic_polynomial.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/CubicPolynomialFitting/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/test_cubic_polynomial.dir/tests/test_cubic_polynomial.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/test_cubic_polynomial.dir/tests/test_cubic_polynomial.cpp.o -MF CMakeFiles/test_cubic_polynomial.dir/tests/test_cubic_polynomial.cpp.o.d -o CMakeFiles/test_cubic_polynomial.dir/tests/test_cubic_polynomial.cpp.o -c /home/<USER>/CubicPolynomialFitting/tests/test_cubic_polynomial.cpp

CMakeFiles/test_cubic_polynomial.dir/tests/test_cubic_polynomial.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/test_cubic_polynomial.dir/tests/test_cubic_polynomial.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/CubicPolynomialFitting/tests/test_cubic_polynomial.cpp > CMakeFiles/test_cubic_polynomial.dir/tests/test_cubic_polynomial.cpp.i

CMakeFiles/test_cubic_polynomial.dir/tests/test_cubic_polynomial.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/test_cubic_polynomial.dir/tests/test_cubic_polynomial.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/CubicPolynomialFitting/tests/test_cubic_polynomial.cpp -o CMakeFiles/test_cubic_polynomial.dir/tests/test_cubic_polynomial.cpp.s

# Object files for target test_cubic_polynomial
test_cubic_polynomial_OBJECTS = \
"CMakeFiles/test_cubic_polynomial.dir/tests/test_cubic_polynomial.cpp.o"

# External object files for target test_cubic_polynomial
test_cubic_polynomial_EXTERNAL_OBJECTS =

test_cubic_polynomial: CMakeFiles/test_cubic_polynomial.dir/tests/test_cubic_polynomial.cpp.o
test_cubic_polynomial: CMakeFiles/test_cubic_polynomial.dir/build.make
test_cubic_polynomial: libcubic_polynomial.a
test_cubic_polynomial: /usr/lib/x86_64-linux-gnu/libgtest.a
test_cubic_polynomial: /usr/lib/x86_64-linux-gnu/libgtest_main.a
test_cubic_polynomial: /usr/lib/x86_64-linux-gnu/libgtest.a
test_cubic_polynomial: CMakeFiles/test_cubic_polynomial.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/CubicPolynomialFitting/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable test_cubic_polynomial"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/test_cubic_polynomial.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/test_cubic_polynomial.dir/build: test_cubic_polynomial
.PHONY : CMakeFiles/test_cubic_polynomial.dir/build

CMakeFiles/test_cubic_polynomial.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/test_cubic_polynomial.dir/cmake_clean.cmake
.PHONY : CMakeFiles/test_cubic_polynomial.dir/clean

CMakeFiles/test_cubic_polynomial.dir/depend:
	cd /home/<USER>/CubicPolynomialFitting/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/CubicPolynomialFitting /home/<USER>/CubicPolynomialFitting /home/<USER>/CubicPolynomialFitting/build /home/<USER>/CubicPolynomialFitting/build /home/<USER>/CubicPolynomialFitting/build/CMakeFiles/test_cubic_polynomial.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/test_cubic_polynomial.dir/depend

