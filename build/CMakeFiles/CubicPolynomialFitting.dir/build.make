# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/CubicPolynomialFitting

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/CubicPolynomialFitting/build

# Include any dependencies generated for this target.
include CMakeFiles/CubicPolynomialFitting.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/CubicPolynomialFitting.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/CubicPolynomialFitting.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/CubicPolynomialFitting.dir/flags.make

CMakeFiles/CubicPolynomialFitting.dir/main.cpp.o: CMakeFiles/CubicPolynomialFitting.dir/flags.make
CMakeFiles/CubicPolynomialFitting.dir/main.cpp.o: ../main.cpp
CMakeFiles/CubicPolynomialFitting.dir/main.cpp.o: CMakeFiles/CubicPolynomialFitting.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/CubicPolynomialFitting/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/CubicPolynomialFitting.dir/main.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/CubicPolynomialFitting.dir/main.cpp.o -MF CMakeFiles/CubicPolynomialFitting.dir/main.cpp.o.d -o CMakeFiles/CubicPolynomialFitting.dir/main.cpp.o -c /home/<USER>/CubicPolynomialFitting/main.cpp

CMakeFiles/CubicPolynomialFitting.dir/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/CubicPolynomialFitting.dir/main.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/CubicPolynomialFitting/main.cpp > CMakeFiles/CubicPolynomialFitting.dir/main.cpp.i

CMakeFiles/CubicPolynomialFitting.dir/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/CubicPolynomialFitting.dir/main.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/CubicPolynomialFitting/main.cpp -o CMakeFiles/CubicPolynomialFitting.dir/main.cpp.s

# Object files for target CubicPolynomialFitting
CubicPolynomialFitting_OBJECTS = \
"CMakeFiles/CubicPolynomialFitting.dir/main.cpp.o"

# External object files for target CubicPolynomialFitting
CubicPolynomialFitting_EXTERNAL_OBJECTS =

CubicPolynomialFitting: CMakeFiles/CubicPolynomialFitting.dir/main.cpp.o
CubicPolynomialFitting: CMakeFiles/CubicPolynomialFitting.dir/build.make
CubicPolynomialFitting: libcubic_polynomial.a
CubicPolynomialFitting: CMakeFiles/CubicPolynomialFitting.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/CubicPolynomialFitting/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable CubicPolynomialFitting"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/CubicPolynomialFitting.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/CubicPolynomialFitting.dir/build: CubicPolynomialFitting
.PHONY : CMakeFiles/CubicPolynomialFitting.dir/build

CMakeFiles/CubicPolynomialFitting.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/CubicPolynomialFitting.dir/cmake_clean.cmake
.PHONY : CMakeFiles/CubicPolynomialFitting.dir/clean

CMakeFiles/CubicPolynomialFitting.dir/depend:
	cd /home/<USER>/CubicPolynomialFitting/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/CubicPolynomialFitting /home/<USER>/CubicPolynomialFitting /home/<USER>/CubicPolynomialFitting/build /home/<USER>/CubicPolynomialFitting/build /home/<USER>/CubicPolynomialFitting/build/CMakeFiles/CubicPolynomialFitting.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/CubicPolynomialFitting.dir/depend

