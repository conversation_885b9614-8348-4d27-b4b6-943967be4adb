# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/CubicPolynomialFitting

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/CubicPolynomialFitting/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/cubic_polynomial.dir/all
all: CMakeFiles/CubicPolynomialFitting.dir/all
all: CMakeFiles/demo_2d.dir/all
all: CMakeFiles/test_cubic_polynomial.dir/all
all: CMakeFiles/test_cubic_polynomial_2d.dir/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/cubic_polynomial.dir/clean
clean: CMakeFiles/CubicPolynomialFitting.dir/clean
clean: CMakeFiles/demo_2d.dir/clean
clean: CMakeFiles/test_cubic_polynomial.dir/clean
clean: CMakeFiles/test_cubic_polynomial_2d.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/cubic_polynomial.dir

# All Build rule for target.
CMakeFiles/cubic_polynomial.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cubic_polynomial.dir/build.make CMakeFiles/cubic_polynomial.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cubic_polynomial.dir/build.make CMakeFiles/cubic_polynomial.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/CubicPolynomialFitting/build/CMakeFiles --progress-num=3,4,5 "Built target cubic_polynomial"
.PHONY : CMakeFiles/cubic_polynomial.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/cubic_polynomial.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/CubicPolynomialFitting/build/CMakeFiles 3
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/cubic_polynomial.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/CubicPolynomialFitting/build/CMakeFiles 0
.PHONY : CMakeFiles/cubic_polynomial.dir/rule

# Convenience name for target.
cubic_polynomial: CMakeFiles/cubic_polynomial.dir/rule
.PHONY : cubic_polynomial

# clean rule for target.
CMakeFiles/cubic_polynomial.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cubic_polynomial.dir/build.make CMakeFiles/cubic_polynomial.dir/clean
.PHONY : CMakeFiles/cubic_polynomial.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/CubicPolynomialFitting.dir

# All Build rule for target.
CMakeFiles/CubicPolynomialFitting.dir/all: CMakeFiles/cubic_polynomial.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CubicPolynomialFitting.dir/build.make CMakeFiles/CubicPolynomialFitting.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CubicPolynomialFitting.dir/build.make CMakeFiles/CubicPolynomialFitting.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/CubicPolynomialFitting/build/CMakeFiles --progress-num=1,2 "Built target CubicPolynomialFitting"
.PHONY : CMakeFiles/CubicPolynomialFitting.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/CubicPolynomialFitting.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/CubicPolynomialFitting/build/CMakeFiles 5
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/CubicPolynomialFitting.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/CubicPolynomialFitting/build/CMakeFiles 0
.PHONY : CMakeFiles/CubicPolynomialFitting.dir/rule

# Convenience name for target.
CubicPolynomialFitting: CMakeFiles/CubicPolynomialFitting.dir/rule
.PHONY : CubicPolynomialFitting

# clean rule for target.
CMakeFiles/CubicPolynomialFitting.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CubicPolynomialFitting.dir/build.make CMakeFiles/CubicPolynomialFitting.dir/clean
.PHONY : CMakeFiles/CubicPolynomialFitting.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/demo_2d.dir

# All Build rule for target.
CMakeFiles/demo_2d.dir/all: CMakeFiles/cubic_polynomial.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/demo_2d.dir/build.make CMakeFiles/demo_2d.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/demo_2d.dir/build.make CMakeFiles/demo_2d.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/CubicPolynomialFitting/build/CMakeFiles --progress-num=6,7 "Built target demo_2d"
.PHONY : CMakeFiles/demo_2d.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/demo_2d.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/CubicPolynomialFitting/build/CMakeFiles 5
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/demo_2d.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/CubicPolynomialFitting/build/CMakeFiles 0
.PHONY : CMakeFiles/demo_2d.dir/rule

# Convenience name for target.
demo_2d: CMakeFiles/demo_2d.dir/rule
.PHONY : demo_2d

# clean rule for target.
CMakeFiles/demo_2d.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/demo_2d.dir/build.make CMakeFiles/demo_2d.dir/clean
.PHONY : CMakeFiles/demo_2d.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/test_cubic_polynomial.dir

# All Build rule for target.
CMakeFiles/test_cubic_polynomial.dir/all: CMakeFiles/cubic_polynomial.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_cubic_polynomial.dir/build.make CMakeFiles/test_cubic_polynomial.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_cubic_polynomial.dir/build.make CMakeFiles/test_cubic_polynomial.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/CubicPolynomialFitting/build/CMakeFiles --progress-num=8,9 "Built target test_cubic_polynomial"
.PHONY : CMakeFiles/test_cubic_polynomial.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/test_cubic_polynomial.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/CubicPolynomialFitting/build/CMakeFiles 5
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/test_cubic_polynomial.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/CubicPolynomialFitting/build/CMakeFiles 0
.PHONY : CMakeFiles/test_cubic_polynomial.dir/rule

# Convenience name for target.
test_cubic_polynomial: CMakeFiles/test_cubic_polynomial.dir/rule
.PHONY : test_cubic_polynomial

# clean rule for target.
CMakeFiles/test_cubic_polynomial.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_cubic_polynomial.dir/build.make CMakeFiles/test_cubic_polynomial.dir/clean
.PHONY : CMakeFiles/test_cubic_polynomial.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/test_cubic_polynomial_2d.dir

# All Build rule for target.
CMakeFiles/test_cubic_polynomial_2d.dir/all: CMakeFiles/cubic_polynomial.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_cubic_polynomial_2d.dir/build.make CMakeFiles/test_cubic_polynomial_2d.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_cubic_polynomial_2d.dir/build.make CMakeFiles/test_cubic_polynomial_2d.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/CubicPolynomialFitting/build/CMakeFiles --progress-num=10,11 "Built target test_cubic_polynomial_2d"
.PHONY : CMakeFiles/test_cubic_polynomial_2d.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/test_cubic_polynomial_2d.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/CubicPolynomialFitting/build/CMakeFiles 5
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/test_cubic_polynomial_2d.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/CubicPolynomialFitting/build/CMakeFiles 0
.PHONY : CMakeFiles/test_cubic_polynomial_2d.dir/rule

# Convenience name for target.
test_cubic_polynomial_2d: CMakeFiles/test_cubic_polynomial_2d.dir/rule
.PHONY : test_cubic_polynomial_2d

# clean rule for target.
CMakeFiles/test_cubic_polynomial_2d.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_cubic_polynomial_2d.dir/build.make CMakeFiles/test_cubic_polynomial_2d.dir/clean
.PHONY : CMakeFiles/test_cubic_polynomial_2d.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

