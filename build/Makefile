# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/CubicPolynomialFitting

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/CubicPolynomialFitting/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/CubicPolynomialFitting/build/CMakeFiles /home/<USER>/CubicPolynomialFitting/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/CubicPolynomialFitting/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named cubic_polynomial

# Build rule for target.
cubic_polynomial: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 cubic_polynomial
.PHONY : cubic_polynomial

# fast build rule for target.
cubic_polynomial/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cubic_polynomial.dir/build.make CMakeFiles/cubic_polynomial.dir/build
.PHONY : cubic_polynomial/fast

#=============================================================================
# Target rules for targets named CubicPolynomialFitting

# Build rule for target.
CubicPolynomialFitting: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CubicPolynomialFitting
.PHONY : CubicPolynomialFitting

# fast build rule for target.
CubicPolynomialFitting/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CubicPolynomialFitting.dir/build.make CMakeFiles/CubicPolynomialFitting.dir/build
.PHONY : CubicPolynomialFitting/fast

#=============================================================================
# Target rules for targets named demo_2d

# Build rule for target.
demo_2d: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 demo_2d
.PHONY : demo_2d

# fast build rule for target.
demo_2d/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/demo_2d.dir/build.make CMakeFiles/demo_2d.dir/build
.PHONY : demo_2d/fast

#=============================================================================
# Target rules for targets named test_cubic_polynomial

# Build rule for target.
test_cubic_polynomial: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test_cubic_polynomial
.PHONY : test_cubic_polynomial

# fast build rule for target.
test_cubic_polynomial/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_cubic_polynomial.dir/build.make CMakeFiles/test_cubic_polynomial.dir/build
.PHONY : test_cubic_polynomial/fast

#=============================================================================
# Target rules for targets named test_cubic_polynomial_2d

# Build rule for target.
test_cubic_polynomial_2d: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test_cubic_polynomial_2d
.PHONY : test_cubic_polynomial_2d

# fast build rule for target.
test_cubic_polynomial_2d/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_cubic_polynomial_2d.dir/build.make CMakeFiles/test_cubic_polynomial_2d.dir/build
.PHONY : test_cubic_polynomial_2d/fast

demo_2d.o: demo_2d.cpp.o
.PHONY : demo_2d.o

# target to build an object file
demo_2d.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/demo_2d.dir/build.make CMakeFiles/demo_2d.dir/demo_2d.cpp.o
.PHONY : demo_2d.cpp.o

demo_2d.i: demo_2d.cpp.i
.PHONY : demo_2d.i

# target to preprocess a source file
demo_2d.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/demo_2d.dir/build.make CMakeFiles/demo_2d.dir/demo_2d.cpp.i
.PHONY : demo_2d.cpp.i

demo_2d.s: demo_2d.cpp.s
.PHONY : demo_2d.s

# target to generate assembly for a file
demo_2d.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/demo_2d.dir/build.make CMakeFiles/demo_2d.dir/demo_2d.cpp.s
.PHONY : demo_2d.cpp.s

main.o: main.cpp.o
.PHONY : main.o

# target to build an object file
main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CubicPolynomialFitting.dir/build.make CMakeFiles/CubicPolynomialFitting.dir/main.cpp.o
.PHONY : main.cpp.o

main.i: main.cpp.i
.PHONY : main.i

# target to preprocess a source file
main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CubicPolynomialFitting.dir/build.make CMakeFiles/CubicPolynomialFitting.dir/main.cpp.i
.PHONY : main.cpp.i

main.s: main.cpp.s
.PHONY : main.s

# target to generate assembly for a file
main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CubicPolynomialFitting.dir/build.make CMakeFiles/CubicPolynomialFitting.dir/main.cpp.s
.PHONY : main.cpp.s

src/cubic_polynomial.o: src/cubic_polynomial.cpp.o
.PHONY : src/cubic_polynomial.o

# target to build an object file
src/cubic_polynomial.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cubic_polynomial.dir/build.make CMakeFiles/cubic_polynomial.dir/src/cubic_polynomial.cpp.o
.PHONY : src/cubic_polynomial.cpp.o

src/cubic_polynomial.i: src/cubic_polynomial.cpp.i
.PHONY : src/cubic_polynomial.i

# target to preprocess a source file
src/cubic_polynomial.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cubic_polynomial.dir/build.make CMakeFiles/cubic_polynomial.dir/src/cubic_polynomial.cpp.i
.PHONY : src/cubic_polynomial.cpp.i

src/cubic_polynomial.s: src/cubic_polynomial.cpp.s
.PHONY : src/cubic_polynomial.s

# target to generate assembly for a file
src/cubic_polynomial.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cubic_polynomial.dir/build.make CMakeFiles/cubic_polynomial.dir/src/cubic_polynomial.cpp.s
.PHONY : src/cubic_polynomial.cpp.s

src/cubic_polynomial_2d.o: src/cubic_polynomial_2d.cpp.o
.PHONY : src/cubic_polynomial_2d.o

# target to build an object file
src/cubic_polynomial_2d.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cubic_polynomial.dir/build.make CMakeFiles/cubic_polynomial.dir/src/cubic_polynomial_2d.cpp.o
.PHONY : src/cubic_polynomial_2d.cpp.o

src/cubic_polynomial_2d.i: src/cubic_polynomial_2d.cpp.i
.PHONY : src/cubic_polynomial_2d.i

# target to preprocess a source file
src/cubic_polynomial_2d.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cubic_polynomial.dir/build.make CMakeFiles/cubic_polynomial.dir/src/cubic_polynomial_2d.cpp.i
.PHONY : src/cubic_polynomial_2d.cpp.i

src/cubic_polynomial_2d.s: src/cubic_polynomial_2d.cpp.s
.PHONY : src/cubic_polynomial_2d.s

# target to generate assembly for a file
src/cubic_polynomial_2d.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cubic_polynomial.dir/build.make CMakeFiles/cubic_polynomial.dir/src/cubic_polynomial_2d.cpp.s
.PHONY : src/cubic_polynomial_2d.cpp.s

tests/test_cubic_polynomial.o: tests/test_cubic_polynomial.cpp.o
.PHONY : tests/test_cubic_polynomial.o

# target to build an object file
tests/test_cubic_polynomial.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_cubic_polynomial.dir/build.make CMakeFiles/test_cubic_polynomial.dir/tests/test_cubic_polynomial.cpp.o
.PHONY : tests/test_cubic_polynomial.cpp.o

tests/test_cubic_polynomial.i: tests/test_cubic_polynomial.cpp.i
.PHONY : tests/test_cubic_polynomial.i

# target to preprocess a source file
tests/test_cubic_polynomial.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_cubic_polynomial.dir/build.make CMakeFiles/test_cubic_polynomial.dir/tests/test_cubic_polynomial.cpp.i
.PHONY : tests/test_cubic_polynomial.cpp.i

tests/test_cubic_polynomial.s: tests/test_cubic_polynomial.cpp.s
.PHONY : tests/test_cubic_polynomial.s

# target to generate assembly for a file
tests/test_cubic_polynomial.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_cubic_polynomial.dir/build.make CMakeFiles/test_cubic_polynomial.dir/tests/test_cubic_polynomial.cpp.s
.PHONY : tests/test_cubic_polynomial.cpp.s

tests/test_cubic_polynomial_2d.o: tests/test_cubic_polynomial_2d.cpp.o
.PHONY : tests/test_cubic_polynomial_2d.o

# target to build an object file
tests/test_cubic_polynomial_2d.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_cubic_polynomial_2d.dir/build.make CMakeFiles/test_cubic_polynomial_2d.dir/tests/test_cubic_polynomial_2d.cpp.o
.PHONY : tests/test_cubic_polynomial_2d.cpp.o

tests/test_cubic_polynomial_2d.i: tests/test_cubic_polynomial_2d.cpp.i
.PHONY : tests/test_cubic_polynomial_2d.i

# target to preprocess a source file
tests/test_cubic_polynomial_2d.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_cubic_polynomial_2d.dir/build.make CMakeFiles/test_cubic_polynomial_2d.dir/tests/test_cubic_polynomial_2d.cpp.i
.PHONY : tests/test_cubic_polynomial_2d.cpp.i

tests/test_cubic_polynomial_2d.s: tests/test_cubic_polynomial_2d.cpp.s
.PHONY : tests/test_cubic_polynomial_2d.s

# target to generate assembly for a file
tests/test_cubic_polynomial_2d.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_cubic_polynomial_2d.dir/build.make CMakeFiles/test_cubic_polynomial_2d.dir/tests/test_cubic_polynomial_2d.cpp.s
.PHONY : tests/test_cubic_polynomial_2d.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... CubicPolynomialFitting"
	@echo "... cubic_polynomial"
	@echo "... demo_2d"
	@echo "... test_cubic_polynomial"
	@echo "... test_cubic_polynomial_2d"
	@echo "... demo_2d.o"
	@echo "... demo_2d.i"
	@echo "... demo_2d.s"
	@echo "... main.o"
	@echo "... main.i"
	@echo "... main.s"
	@echo "... src/cubic_polynomial.o"
	@echo "... src/cubic_polynomial.i"
	@echo "... src/cubic_polynomial.s"
	@echo "... src/cubic_polynomial_2d.o"
	@echo "... src/cubic_polynomial_2d.i"
	@echo "... src/cubic_polynomial_2d.s"
	@echo "... tests/test_cubic_polynomial.o"
	@echo "... tests/test_cubic_polynomial.i"
	@echo "... tests/test_cubic_polynomial.s"
	@echo "... tests/test_cubic_polynomial_2d.o"
	@echo "... tests/test_cubic_polynomial_2d.i"
	@echo "... tests/test_cubic_polynomial_2d.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

