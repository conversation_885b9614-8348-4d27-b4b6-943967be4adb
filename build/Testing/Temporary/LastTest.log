Start testing: Jun 21 14:26 JST
----------------------------------------------------------
1/2 Testing: CubicPolynomialTests
1/2 Test: CubicPolynomialTests
Command: "/home/<USER>/CubicPolynomialFitting/build/test_cubic_polynomial"
Directory: /home/<USER>/CubicPolynomialFitting/build
"CubicPolynomialTests" start time: Jun 21 14:26 JST
Output:
----------------------------------------------------------
[==========] Running 9 tests from 1 test suite.
[----------] Global test environment set-up.
[----------] 9 tests from CubicPolynomialTest
[ RUN      ] CubicPolynomialTest.EvaluateBasic
[       OK ] CubicPolynomialTest.EvaluateBasic (0 ms)
[ RUN      ] CubicPolynomialTest.EvaluateDerivative
[       OK ] CubicPolynomialTest.EvaluateDerivative (0 ms)
[ RUN      ] CubicPolynomialTest.EvaluateSecondDerivative
[       OK ] CubicPolynomialTest.EvaluateSecondDerivative (0 ms)
[ RUN      ] CubicPolynomialTest.FitFourPoints
[       OK ] CubicPolynomialTest.FitFourPoints (0 ms)
[ RUN      ] CubicPolynomialTest.FitFourPointsInvalidInput
[       OK ] CubicPolynomialTest.FitFourPointsInvalidInput (0 ms)
[ RUN      ] CubicPolynomialTest.FitPointHeadingPointHeading
[       OK ] CubicPolynomialTest.FitPointHeadingPointHeading (0 ms)
[ RUN      ] CubicPolynomialTest.ToString
[       OK ] CubicPolynomialTest.ToString (0 ms)
[ RUN      ] CubicPolynomialTest.EvaluateMultiple
[       OK ] CubicPolynomialTest.EvaluateMultiple (0 ms)
[ RUN      ] CubicPolynomialTest.LinearPolynomial
[       OK ] CubicPolynomialTest.LinearPolynomial (0 ms)
[----------] 9 tests from CubicPolynomialTest (0 ms total)

[----------] Global test environment tear-down
[==========] 9 tests from 1 test suite ran. (0 ms total)
[  PASSED  ] 9 tests.
<end of output>
Test time =   0.00 sec
----------------------------------------------------------
Test Passed.
"CubicPolynomialTests" end time: Jun 21 14:26 JST
"CubicPolynomialTests" time elapsed: 00:00:00
----------------------------------------------------------

2/2 Testing: CubicPolynomial2DTests
2/2 Test: CubicPolynomial2DTests
Command: "/home/<USER>/CubicPolynomialFitting/build/test_cubic_polynomial_2d"
Directory: /home/<USER>/CubicPolynomialFitting/build
"CubicPolynomial2DTests" start time: Jun 21 14:26 JST
Output:
----------------------------------------------------------
[==========] Running 10 tests from 1 test suite.
[----------] Global test environment set-up.
[----------] 10 tests from CubicPolynomial2DTest
[ RUN      ] CubicPolynomial2DTest.EvaluateBasic
[       OK ] CubicPolynomial2DTest.EvaluateBasic (0 ms)
[ RUN      ] CubicPolynomial2DTest.EvaluateDerivative
[       OK ] CubicPolynomial2DTest.EvaluateDerivative (0 ms)
[ RUN      ] CubicPolynomial2DTest.EvaluateSecondDerivative
[       OK ] CubicPolynomial2DTest.EvaluateSecondDerivative (0 ms)
[ RUN      ] CubicPolynomial2DTest.FitFourPoints2D
[       OK ] CubicPolynomial2DTest.FitFourPoints2D (0 ms)
[ RUN      ] CubicPolynomial2DTest.FitFourPoints2DInvalidInput
[       OK ] CubicPolynomial2DTest.FitFourPoints2DInvalidInput (0 ms)
[ RUN      ] CubicPolynomial2DTest.FitPointHeadingPointHeading2D
[       OK ] CubicPolynomial2DTest.FitPointHeadingPointHeading2D (0 ms)
[ RUN      ] CubicPolynomial2DTest.ToString
[       OK ] CubicPolynomial2DTest.ToString (0 ms)
[ RUN      ] CubicPolynomial2DTest.EvaluateMultiple
[       OK ] CubicPolynomial2DTest.EvaluateMultiple (0 ms)
[ RUN      ] CubicPolynomial2DTest.CoefficientsUtilities
[       OK ] CubicPolynomial2DTest.CoefficientsUtilities (0 ms)
[ RUN      ] CubicPolynomial2DTest.LinearPolynomial2D
[       OK ] CubicPolynomial2DTest.LinearPolynomial2D (0 ms)
[----------] 10 tests from CubicPolynomial2DTest (0 ms total)

[----------] Global test environment tear-down
[==========] 10 tests from 1 test suite ran. (0 ms total)
[  PASSED  ] 10 tests.
<end of output>
Test time =   0.00 sec
----------------------------------------------------------
Test Passed.
"CubicPolynomial2DTests" end time: Jun 21 14:26 JST
"CubicPolynomial2DTests" time elapsed: 00:00:00
----------------------------------------------------------

End testing: Jun 21 14:26 JST
