cmake_minimum_required(VERSION 3.16)
project(CubicPolynomialFitting)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required packages
find_package(Eigen3 REQUIRED)
find_package(GTest REQUIRED)

# Include directories
include_directories(include)

# Create the cubic polynomial library
add_library(cubic_polynomial STATIC
    src/cubic_polynomial.cpp
    src/cubic_polynomial_2d.cpp
)

# Link Eigen to the library
target_link_libraries(cubic_polynomial Eigen3::Eigen)

# Create the main executable
add_executable(CubicPolynomialFitting main.cpp)
target_link_libraries(CubicPolynomialFitting cubic_polynomial)

# Create the 2D demo executable
add_executable(demo_2d demo_2d.cpp)
target_link_libraries(demo_2d cubic_polynomial)

# Create the test executable
add_executable(test_cubic_polynomial tests/test_cubic_polynomial.cpp)
target_link_libraries(test_cubic_polynomial
    cubic_polynomial
    GTest::gtest
    GTest::gtest_main
)

# Create the 2D test executable
add_executable(test_cubic_polynomial_2d tests/test_cubic_polynomial_2d.cpp)
target_link_libraries(test_cubic_polynomial_2d
    cubic_polynomial
    GTest::gtest
    GTest::gtest_main
)

# Enable testing
enable_testing()
add_test(NAME CubicPolynomialTests COMMAND test_cubic_polynomial)
add_test(NAME CubicPolynomial2DTests COMMAND test_cubic_polynomial_2d)
